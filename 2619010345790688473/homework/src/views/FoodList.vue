<template>
  <div class="food-list-container">
    <div class="banner-section">
      <Banner />

    </div>
    
    <h3>主题系列</h3>
    <div class="food-categories">
      <div class="category-item" v-for="(category, index) in categories" :key="index">
        <div class="category-image">
          <img :src="category.image" alt="category" />
        </div>
        <span class="category-name">{{ category.name }}</span>
      </div>
    </div>
    
    <div class="featured-dishes">
      <h3>招牌菜系</h3>
      <div class="cai" v-for="(food, index) in foods" :key="index">
        <div class="cai-raw">
          <div class="cai-columns">
            <img :src="food.image">
            <div class='price'>
              <div >{{food.name}}</div>
              <div >{{food.price}}</div>
            </div>
          </div>
          <div class='count'>
            <button @click="food.count++">+</button>
            <span>{{food.count}}</span>
            <button  @click="food.count--">-</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Banner from '../components/Banners.vue';
import {ref} from 'vue';
import xiangcai from '../asserts/xiangcai.png';
import chuancai from '../picture/7.webp';
import yuecai from '../picture/3.jpg';
import lucai from '../picture/lucai.webp';
import mincai from '../picture/8.webp';

export default {
  components: {
    Banner // 注册组件
  },

  data() {
    return {
      categories: [
        { name: '湘菜系列', image: xiangcai },
        { name: '川菜系列', image: chuancai },
        { name: '粤菜系列', image: yuecai },
        { name: '鲁菜系列', image: lucai },
        { name: '闽菜系列', image: mincai }
      ],
      banners: [
        {image:"/src/asserts/banner1.png"},
        {image:"/src/asserts/banner2.png"},
        {image:"/src/asserts/banner3.png"},
        {image:"/src/asserts/banner4.png"},
      ],
      foods: [
        {name:"毛氏红烧肉", image:"/src/asserts/lucai.png", price:"￥20元", count: ref(0)},
        {name:"佛跳墙", image:"/src/asserts/yuecai.png", price:"￥40元", count: ref(0)},
        {name:"麻婆豆腐", image:"/src/asserts/chuancai.png", price:"￥33元", count: ref(0)},
        {name:"九转大肠", image:"/src/asserts/xiangcai.png", price:"￥20元", count: ref(0)},
      ]
    };
  }
};
</script>

<style scoped>
div {
  border: none;
}

.food-list-container {
  width: 800px;
  margin: auto;
  background-color: #f5f5f5;
  margin-bottom: 50px;
}

.banner-section {
  position: relative;
  margin-bottom: 30px;
}

.banner-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.banner-overlay h2 {
  color: white;
  font-size: 28px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin: 0;
  text-align: center;
}

.food-categories {
  margin: 0 20px 20px 20px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 10px 0;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin: 10px;
}

.category-image {
  width: 100px;
  height: 70px;
  border-radius: 50px/35px;
  overflow: hidden;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.featured-dishes {
  margin: 0 0 20px 0;
}

.featured-dishes h3 {
  margin: 0 20px 15px 20px;
}

.dish-item {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-top: 10px;
}

.cai {
  display: flex;
  flex-flow: column, nowrap;	
  align-items: space-between;
  margin-left: 20px;
}

.cai img {
  width: 100px;
  height: 100px;
}

.cai-raw {
  display: flex;
  width: 100%;
  flex-flow: row, nowrap;	
  justify-content:space-between; 
  margin: 10px;
}

.cai-columns{
  display: flex;
  width: 100%;
  flex-flow: row, nowrap;	
  margin: 10px;
}

.dish-item {
  margin: auto;
}

.count {
  display: flex;
  align-items: flex-end;
}

.count button, span {
  margin: 5px;
}

h3 {
  text-align: left;
  margin: 0 20px 15px 20px;
  color: #333;
  font-size: 18px;
  padding-left: 0;
}

.price {
  display: flex;
  flex-direction: column ;
  align-items:flex-start;
  margin: 10px 20px;
}

.price div {
  margin: 10px;
}
</style>
